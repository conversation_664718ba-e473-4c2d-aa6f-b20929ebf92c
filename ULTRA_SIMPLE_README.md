# Ultra-Simple FramePack API

## 🎯 核心特点

这是一个**极简**的FramePack API，专门为你的需求设计：

- ✅ **模型只初始化一次**
- ✅ **直接调用 `sample_hunyuan`** 生成单个latent section
- ✅ **只需要传入 text 和 image**
- ✅ **返回原始 latent tensor**

## 🚀 快速使用

```python
from simple_framepack import UltraSimpleFramePack

# 1. 初始化一次（加载所有模型）
generator = UltraSimpleFramePack(
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    video_size=(512, 512),
    frames=33  # 单个section的帧数
)

# 2. 生成latent（只需要text和image）
latent = generator.generate_latent(
    prompt="A beautiful sunset over the ocean",
    image="/path/to/image.jpg"  # 或 PIL Image
)

print(f"Generated latent shape: {latent.shape}")
# 输出: Generated latent shape: torch.Size([1, 16, 9, 64, 64])
```

## 🔧 API详解

### 初始化参数

**必需参数：**
- `dit_path`: DiT模型路径
- `vae_path`: VAE模型路径  
- `text_encoder1_path`: 文本编码器1路径
- `text_encoder2_path`: 文本编码器2路径
- `image_encoder_path`: 图像编码器路径

**可选参数：**
- `video_size`: 视频尺寸，默认 (512, 512)
- `frames`: 单section帧数，默认 33
- `infer_steps`: 推理步数，默认 25
- `guidance_scale`: 引导比例，默认 1.0
- `embedded_cfg_scale`: CFG比例，默认 10.0

### 生成方法

```python
latent = generator.generate_latent(
    prompt="文本描述",      # 必需
    image="图像路径或PIL",   # 必需  
    seed=42               # 可选，随机种子
)
```

## 📊 与其他API的对比

| 特性 | 完整API | 简化API | Ultra-Simple API |
|------|---------|---------|------------------|
| 模型加载 | 每次 | 一次 | 一次 |
| 调用函数 | generate() | generate() | **sample_hunyuan()** |
| 输出 | 完整视频 | 完整视频 | **单个latent** |
| 参数数量 | 10+ | 2 | **2** |
| section数量 | 多个 | 多个 | **1个** |

## 🎯 核心实现

这个API直接调用了你想要的核心函数：

```python
# 直接调用 sample_hunyuan（这就是你想要的！）
generated_latents = sample_hunyuan(
    transformer=self.model,
    sampler=args.sample_solver,
    width=width,
    height=height,
    frames=self.frames,
    # ... 其他参数
    image_embeddings=image_encoder_last_hidden_state,
    latent_indices=latent_indices,
    clean_latents=clean_latents,
    # ...
)
```

## 🔄 批量生成示例

```python
# 初始化一次
generator = UltraSimpleFramePack(...)

# 批量生成多个latent
prompts = ["scene 1", "scene 2", "scene 3"]
latents = []

for prompt in prompts:
    latent = generator.generate_latent(
        prompt=prompt,
        image="input.jpg"
    )
    latents.append(latent)
    print(f"Generated: {latent.shape}")

# 现在你有了多个latent tensor，可以进一步处理
```

## 📝 Latent输出格式

- **Shape**: `(1, 16, T, H, W)`
  - `1`: Batch size
  - `16`: Latent channels
  - `T`: 时间维度 (frames + 3) // 4
  - `H`: 高度 / 8
  - `W`: 宽度 / 8

- **Type**: `torch.bfloat16`
- **Device**: 你指定的设备 (cuda/cpu)

## 🚀 快速开始

1. **修改模型路径**：编辑 `test_ultra_simple.py` 中的路径
2. **运行测试**：`python test_ultra_simple.py`
3. **开始使用**：导入 `UltraSimpleFramePack` 并使用

## 💡 使用场景

这个API特别适合：

- **研究和实验**：直接获取latent进行分析
- **自定义后处理**：对latent进行自定义操作
- **批量处理**：高效生成大量latent
- **集成到更大的pipeline**：作为组件使用

## 🎉 总结

现在你可以：
- ✅ 只调用一次 `sample_hunyuan`
- ✅ 生成一个 latent section
- ✅ 模型只初始化一次
- ✅ 只传入 text 和 image

这就是你想要的最简单的API！🎯
