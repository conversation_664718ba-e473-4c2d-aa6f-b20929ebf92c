# 🎯 配置好的 Ultra-Simple FramePack API

## ✨ 已配置的默认参数

我已经将你的所有参数设置为默认值，现在使用起来更加简单！

### 📁 默认模型路径
```python
dit_path = "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7"
vae_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors"
text_encoder1_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
text_encoder2_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773"
image_encoder_path = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--lllyasviel--flux_redux_bfl/snapshots/45b801affc54ff2af4e5daf1b282e0921901db87/image_encoder/model.safetensors"
```

### ⚙️ 默认配置参数
```python
video_size = (512, 768)          # 你的配置：512x768
frames = 33                      # 单section帧数
infer_steps = 25                 # 推理步数
guidance_scale = 1.0             # 引导比例
embedded_cfg_scale = 10.0        # CFG比例
fp8_scaled = True                # 启用FP8 scaled
attn_mode = "sdpa"               # 注意力模式
vae_chunk_size = 32              # VAE chunk大小
vae_spatial_tile_sample_min_size = 128  # VAE tile最小尺寸
f1 = True                        # 启用F1模式
lora_weight = "/media/jiayueru/Codes/musubi-tuner/ckpt/output/test_lora-000005.safetensors"
lora_multiplier = 1.0            # LoRA倍数
seed = 1234                      # 默认种子
```

## 🚀 极简使用方法

### 方法1：完全默认（推荐）
```python
from simple_framepack import UltraSimpleFramePack

# 使用所有默认参数（你的配置）
generator = UltraSimpleFramePack()

# 只需要传入text和image！
latent = generator.generate_latent(
    prompt="A beautiful sunset over the ocean",
    image="/path/to/image.jpg"
)

print(f"Generated latent: {latent.shape}")
```

### 方法2：覆盖部分参数
```python
# 只覆盖需要改变的参数
generator = UltraSimpleFramePack(
    video_size=(256, 256),  # 改变视频尺寸
    frames=17,              # 改变帧数
    seed=42                 # 改变种子
    # 其他参数使用默认值
)

latent = generator.generate_latent(
    prompt="A cat playing in the garden",
    image="/path/to/cat.jpg"
)
```

### 方法3：批量生成
```python
# 初始化一次，生成多次
generator = UltraSimpleFramePack()

prompts = [
    "A sunset over mountains",
    "Birds flying in the sky",
    "Waves crashing on shore"
]

latents = []
for prompt in prompts:
    latent = generator.generate_latent(
        prompt=prompt,
        image="base_image.jpg"
    )
    latents.append(latent)
    print(f"Generated: {latent.shape}")
```

## 🎯 核心优势

### 之前 vs 现在

**之前需要：**
```python
generator = UltraSimpleFramePack(
    dit_path="/very/long/path/to/dit/model",
    vae_path="/very/long/path/to/vae/model",
    text_encoder1_path="/very/long/path/to/text_encoder1",
    text_encoder2_path="/very/long/path/to/text_encoder2",
    image_encoder_path="/very/long/path/to/image_encoder",
    video_size=(512, 768),
    frames=33,
    infer_steps=25,
    fp8_scaled=True,
    attn_mode="sdpa",
    vae_chunk_size=32,
    vae_spatial_tile_sample_min_size=128,
    f1=True,
    lora_weight="/path/to/lora.safetensors",
    lora_multiplier=1.0,
    seed=1234
)
```

**现在只需要：**
```python
generator = UltraSimpleFramePack()  # 所有参数都是默认的！
```

## 📊 参数对照表

| 参数 | 你的配置值 | API默认值 | 说明 |
|------|------------|-----------|------|
| video_size | 512x768 | ✅ (512, 768) | 视频尺寸 |
| infer_steps | 25 | ✅ 25 | 推理步数 |
| fp8_scaled | --fp8_scaled | ✅ True | FP8精度 |
| attn_mode | sdpa | ✅ "sdpa" | 注意力模式 |
| vae_chunk_size | 32 | ✅ 32 | VAE块大小 |
| vae_spatial_tile_sample_min_size | 128 | ✅ 128 | VAE tile大小 |
| f1 | --f1 | ✅ True | F1模式 |
| lora_multiplier | 1.0 | ✅ 1.0 | LoRA倍数 |
| seed | 1234 | ✅ 1234 | 随机种子 |

## 🧪 快速测试

```bash
# 运行测试（使用默认配置）
python test_ultra_simple.py
```

测试脚本会：
1. 检查默认路径是否存在
2. 创建测试图像
3. 使用默认配置初始化生成器
4. 生成测试latent
5. 验证输出格式

## 🎉 总结

现在你可以：

- ✅ **零配置启动**：`UltraSimpleFramePack()` 即可
- ✅ **使用你的所有设置**：所有参数都是你的配置
- ✅ **极简调用**：只需要 text + image
- ✅ **直接调用 sample_hunyuan**：一次生成一个latent section

这就是你想要的**最简单、最直接**的API！🎯
