"""
FramePack API - Simple interface for video generation using FramePack
"""

import os
import tempfile
import torch
from typing import Union, Optional
from PIL import Image
import logging

# Import necessary modules from the existing codebase
from fpack_generate_video import (
    generate,
    get_generation_settings,
    save_output,
    decode_latent
)
from types import SimpleNamespace

logger = logging.getLogger(__name__)


class FramePackGenerator:
    """
    Simple API wrapper for FramePack video generation
    """
    
    def __init__(
        self,
        dit_path: str,
        vae_path: str,
        text_encoder1_path: str,
        text_encoder2_path: str,
        image_encoder_path: str,
        device: Optional[str] = None,
        fp8: bool = False,
        fp8_scaled: bool = False,
        attn_mode: str = "torch"
    ):
        """
        Initialize FramePack Generator
        
        Args:
            dit_path: Path to DiT model
            vae_path: Path to VAE model
            text_encoder1_path: Path to text encoder 1 (LLaMA)
            text_encoder2_path: Path to text encoder 2 (CLIP)
            image_encoder_path: Path to image encoder
            device: Device to use ('cuda', 'cpu', or None for auto)
            fp8: Use FP8 precision
            fp8_scaled: Use scaled FP8 precision
            attn_mode: Attention mode ('torch', 'flash', 'sageattn', 'xformers', 'sdpa')
        """
        self.dit_path = dit_path
        self.vae_path = vae_path
        self.text_encoder1_path = text_encoder1_path
        self.text_encoder2_path = text_encoder2_path
        self.image_encoder_path = image_encoder_path
        
        # Set device
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
            
        self.fp8 = fp8
        self.fp8_scaled = fp8_scaled
        self.attn_mode = attn_mode
        
        logger.info(f"FramePack Generator initialized on device: {self.device}")
    
    def _create_args(
        self,
        prompt: str,
        image_path: str,
        video_size: tuple = (256, 256),
        video_seconds: float = 5.0,
        fps: int = 30,
        infer_steps: int = 25,
        guidance_scale: float = 1.0,
        embedded_cfg_scale: float = 10.0,
        negative_prompt: str = "",
        seed: Optional[int] = None,
        sample_solver: str = "unipc",
        save_path: str = "./output"
    ) -> SimpleNamespace:
        """Create arguments namespace for generation"""
        
        args = SimpleNamespace()
        
        # Model paths
        args.dit = self.dit_path
        args.vae = self.vae_path
        args.text_encoder1 = self.text_encoder1_path
        args.text_encoder2 = self.text_encoder2_path
        args.image_encoder = self.image_encoder_path
        
        # Generation parameters
        args.prompt = prompt
        args.image_path = image_path
        args.video_size = list(video_size)  # [height, width]
        args.video_seconds = video_seconds
        args.fps = fps
        args.infer_steps = infer_steps
        args.guidance_scale = guidance_scale
        args.embedded_cfg_scale = embedded_cfg_scale
        args.negative_prompt = negative_prompt if negative_prompt else ""
        args.seed = seed
        args.sample_solver = sample_solver
        args.save_path = save_path
        
        # Device and precision settings
        args.device = self.device
        args.fp8 = self.fp8
        args.fp8_scaled = self.fp8_scaled
        args.fp8_llm = False
        args.attn_mode = self.attn_mode
        
        # Default settings
        args.latent_window_size = 9
        args.guidance_rescale = 0.0
        args.end_image_path = None
        args.latent_paddings = None
        args.one_frame_inference = None
        args.f1 = False
        args.vae_chunk_size = None
        args.vae_spatial_tile_sample_min_size = None
        args.bulk_decode = False
        args.blocks_to_swap = 0
        args.output_type = "video"
        args.no_metadata = False
        args.video_sections = None
        args.custom_system_prompt = None
        
        # LoRA settings (disabled by default)
        args.lora_weight = None
        args.lora_multiplier = 1.0
        args.include_patterns = None
        args.exclude_patterns = None
        args.save_merged_model = None
        args.lycoris = False
        
        return args

    def generate_video(
        self,
        prompt: str,
        image: Union[str, Image.Image],
        video_size: tuple = (256, 256),
        video_seconds: float = 5.0,
        fps: int = 30,
        infer_steps: int = 25,
        guidance_scale: float = 1.0,
        embedded_cfg_scale: float = 10.0,
        negative_prompt: str = "",
        seed: Optional[int] = None,
        sample_solver: str = "unipc",
        save_path: str = "./output",
        return_video_path: bool = True
    ) -> Union[str, torch.Tensor]:
        """
        Generate video from image and text prompt

        Args:
            prompt: Text prompt for video generation
            image: Input image (PIL Image or path to image file)
            video_size: Video dimensions as (height, width)
            video_seconds: Duration of video in seconds
            fps: Frames per second
            infer_steps: Number of inference steps
            guidance_scale: Guidance scale for generation
            embedded_cfg_scale: Embedded CFG scale
            negative_prompt: Negative prompt
            seed: Random seed (None for random)
            sample_solver: Sampling solver ('unipc', 'dpm++', 'vanilla')
            save_path: Directory to save output
            return_video_path: If True, return path to saved video; if False, return video tensor

        Returns:
            str or torch.Tensor: Path to generated video file or video tensor
        """

        # Handle image input
        if isinstance(image, Image.Image):
            # Save PIL image to temporary file
            temp_dir = tempfile.mkdtemp()
            image_path = os.path.join(temp_dir, "input_image.png")
            image.save(image_path)
        elif isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            image_path = image
        else:
            raise ValueError("Image must be a PIL Image or path to image file")

        # Create arguments
        args = self._create_args(
            prompt=prompt,
            image_path=image_path,
            video_size=video_size,
            video_seconds=video_seconds,
            fps=fps,
            infer_steps=infer_steps,
            guidance_scale=guidance_scale,
            embedded_cfg_scale=embedded_cfg_scale,
            negative_prompt=negative_prompt,
            seed=seed,
            sample_solver=sample_solver,
            save_path=save_path
        )

        # Generate video
        logger.info(f"Starting video generation with prompt: '{prompt}'")
        gen_settings = get_generation_settings(args)
        vae, latent = generate(args, gen_settings)

        if return_video_path:
            # Save and return path
            save_output(args, vae, latent[0], self.device)

            # Find the generated video file
            import glob
            video_files = glob.glob(os.path.join(save_path, "*.mp4"))
            if video_files:
                latest_video = max(video_files, key=os.path.getctime)
                logger.info(f"Video generated successfully: {latest_video}")
                return latest_video
            else:
                raise RuntimeError("Video generation completed but no video file found")
        else:
            # Return video tensor
            total_latent_sections = (video_seconds * 30) / (args.latent_window_size * 4)
            total_latent_sections = int(max(round(total_latent_sections), 1))
            video_tensor = decode_latent(
                args.latent_window_size,
                total_latent_sections,
                args.bulk_decode,
                vae,
                latent,
                self.device,
                args.one_frame_inference is not None
            )
            logger.info(f"Video tensor generated with shape: {video_tensor.shape}")
            return video_tensor


def create_framepack_generator(
    dit_path: str,
    vae_path: str,
    text_encoder1_path: str,
    text_encoder2_path: str,
    image_encoder_path: str,
    **kwargs
) -> FramePackGenerator:
    """
    Convenience function to create a FramePackGenerator instance

    Args:
        dit_path: Path to DiT model
        vae_path: Path to VAE model
        text_encoder1_path: Path to text encoder 1 (LLaMA)
        text_encoder2_path: Path to text encoder 2 (CLIP)
        image_encoder_path: Path to image encoder
        **kwargs: Additional arguments for FramePackGenerator

    Returns:
        FramePackGenerator: Configured generator instance
    """
    return FramePackGenerator(
        dit_path=dit_path,
        vae_path=vae_path,
        text_encoder1_path=text_encoder1_path,
        text_encoder2_path=text_encoder2_path,
        image_encoder_path=image_encoder_path,
        **kwargs
    )


def generate_video_simple(
    prompt: str,
    image: Union[str, Image.Image],
    dit_path: str,
    vae_path: str,
    text_encoder1_path: str,
    text_encoder2_path: str,
    image_encoder_path: str,
    **kwargs
) -> str:
    """
    Simple one-shot function to generate video

    Args:
        prompt: Text prompt for video generation
        image: Input image (PIL Image or path to image file)
        dit_path: Path to DiT model
        vae_path: Path to VAE model
        text_encoder1_path: Path to text encoder 1 (LLaMA)
        text_encoder2_path: Path to text encoder 2 (CLIP)
        image_encoder_path: Path to image encoder
        **kwargs: Additional generation parameters

    Returns:
        str: Path to generated video file
    """
    generator = create_framepack_generator(
        dit_path=dit_path,
        vae_path=vae_path,
        text_encoder1_path=text_encoder1_path,
        text_encoder2_path=text_encoder2_path,
        image_encoder_path=image_encoder_path
    )

    return generator.generate_video(prompt=prompt, image=image, **kwargs)


# Example usage
if __name__ == "__main__":
    # Example of how to use the FramePack API

    # Method 1: Using the class directly
    generator = FramePackGenerator(
        dit_path="/path/to/dit/model",
        vae_path="/path/to/vae/model",
        text_encoder1_path="/path/to/text_encoder1",
        text_encoder2_path="/path/to/text_encoder2",
        image_encoder_path="/path/to/image_encoder",
        device="cuda",  # or "cpu"
        fp8=False,
        attn_mode="torch"
    )

    # Generate video from image and text
    video_path = generator.generate_video(
        prompt="A beautiful sunset over the ocean with gentle waves",
        image="/path/to/input/image.jpg",  # or PIL Image object
        video_size=(512, 512),
        video_seconds=5.0,
        fps=30,
        infer_steps=25,
        seed=42,
        save_path="./output"
    )

    print(f"Generated video saved to: {video_path}")

    # Method 2: Using the convenience function
    video_path2 = generate_video_simple(
        prompt="A cat playing in the garden",
        image="/path/to/cat_image.jpg",
        dit_path="/path/to/dit/model",
        vae_path="/path/to/vae/model",
        text_encoder1_path="/path/to/text_encoder1",
        text_encoder2_path="/path/to/text_encoder2",
        image_encoder_path="/path/to/image_encoder",
        video_size=(256, 256),
        video_seconds=3.0,
        save_path="./output"
    )

    print(f"Generated video 2 saved to: {video_path2}")
