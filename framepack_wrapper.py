"""
FramePack API - Simplified interface for video generation using FramePack
"""

import os
import tempfile
import torch
from typing import Union, Optional
from PIL import Image
import logging
import glob

# Import necessary modules from the existing codebase
from fpack_generate_video import (
    generate,
    get_generation_settings,
    save_output
)
from frame_pack.framepack_utils import load_vae
from types import SimpleNamespace

logger = logging.getLogger(__name__)


class SimpleFramePackGenerator:
    """
    Simplified API wrapper for FramePack video generation
    Models are loaded once during initialization, then generate() only needs text and image
    """

    def __init__(
        self,
        dit_path: str,
        vae_path: str,
        text_encoder1_path: str,
        text_encoder2_path: str,
        image_encoder_path: str,
        device: Optional[str] = None,
        video_size: tuple = (512, 512),
        video_frames: int = 33,  # Fixed frame count (about 1 second at 30fps)
        fps: int = 30,
        infer_steps: int = 25,
        guidance_scale: float = 1.0,
        embedded_cfg_scale: float = 10.0,
        fp8: bool = False,
        fp8_scaled: bool = False,
        attn_mode: str = "torch"
    ):
        """
        Initialize FramePack Generator with all models loaded

        Args:
            dit_path: Path to DiT model
            vae_path: Path to VAE model
            text_encoder1_path: Path to text encoder 1 (LLaMA)
            text_encoder2_path: Path to text encoder 2 (CLIP)
            image_encoder_path: Path to image encoder
            device: Device to use ('cuda', 'cpu', or None for auto)
            video_size: Fixed video dimensions as (height, width)
            video_frames: Fixed number of frames to generate
            fps: Fixed frames per second
            infer_steps: Fixed number of inference steps
            guidance_scale: Fixed guidance scale
            embedded_cfg_scale: Fixed embedded CFG scale
            fp8: Use FP8 precision
            fp8_scaled: Use scaled FP8 precision
            attn_mode: Attention mode
        """
        # Set device
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        # Store fixed parameters
        self.video_size = video_size
        self.video_frames = video_frames
        self.fps = fps
        self.infer_steps = infer_steps
        self.guidance_scale = guidance_scale
        self.embedded_cfg_scale = embedded_cfg_scale
        self.video_seconds = video_frames / fps

        logger.info(f"Initializing SimpleFramePackGenerator on device: {self.device}")
        logger.info(f"Fixed parameters: {video_size}@{video_frames}frames, {fps}fps, {infer_steps}steps")

        # Create base args for model loading
        self.base_args = self._create_base_args(
            dit_path, vae_path, text_encoder1_path, text_encoder2_path,
            image_encoder_path, fp8, fp8_scaled, attn_mode
        )

        # Load all models once
        logger.info("Loading models...")
        self._load_models()
        logger.info("All models loaded successfully!")

    def _create_base_args(
        self, dit_path, vae_path, text_encoder1_path, text_encoder2_path,
        image_encoder_path, fp8, fp8_scaled, attn_mode
    ) -> SimpleNamespace:
        """Create base arguments for model loading"""
        args = SimpleNamespace()

        # Model paths
        args.dit = dit_path
        args.vae = vae_path
        args.text_encoder1 = text_encoder1_path
        args.text_encoder2 = text_encoder2_path
        args.image_encoder = image_encoder_path

        # Device and precision settings
        args.device = self.device
        args.fp8 = fp8
        args.fp8_scaled = fp8_scaled
        args.fp8_llm = False
        args.attn_mode = attn_mode

        # Default settings for model loading
        args.blocks_to_swap = 0
        args.vae_chunk_size = None
        args.vae_spatial_tile_sample_min_size = None
        args.lora_weight = None
        args.save_merged_model = None

        return args

    def _load_models(self):
        """Load all models once during initialization"""
        from frame_pack.framepack_utils import load_vae

        # Load VAE
        logger.info("Loading VAE...")
        self.vae = load_vae(
            self.base_args.vae,
            self.base_args.vae_chunk_size,
            self.base_args.vae_spatial_tile_sample_min_size,
            self.device
        )

        # Load DiT model
        logger.info("Loading DiT model...")
        from fpack_generate_video import load_dit_model, optimize_model
        self.model = load_dit_model(self.base_args, self.device)
        optimize_model(self.model, self.base_args, self.device)

        logger.info("Models loaded and ready for generation!")

    def _create_generation_args(
        self, prompt: str, image_path: str, seed: Optional[int] = None
    ) -> SimpleNamespace:
        """Create arguments namespace for generation using fixed parameters"""

        args = SimpleNamespace()

        # Copy base args
        for attr in vars(self.base_args):
            setattr(args, attr, getattr(self.base_args, attr))

        # Generation parameters (using fixed values from initialization)
        args.prompt = prompt
        args.image_path = image_path
        args.video_size = list(self.video_size)  # [height, width]
        args.video_seconds = self.video_seconds
        args.fps = self.fps
        args.infer_steps = self.infer_steps
        args.guidance_scale = self.guidance_scale
        args.embedded_cfg_scale = self.embedded_cfg_scale
        args.negative_prompt = ""  # Fixed empty negative prompt
        args.seed = seed
        args.sample_solver = "unipc"  # Fixed solver
        args.save_path = "./output"  # Fixed output path

        # Default settings
        args.latent_window_size = 9
        args.guidance_rescale = 0.0
        args.end_image_path = None
        args.latent_paddings = None
        args.one_frame_inference = None
        args.f1 = False
        args.bulk_decode = False
        args.output_type = "video"
        args.no_metadata = False
        args.video_sections = None
        args.custom_system_prompt = None

        # LoRA settings (disabled)
        args.lora_multiplier = 1.0
        args.include_patterns = None
        args.exclude_patterns = None
        args.lycoris = False

        return args

    def generate(
        self,
        prompt: str,
        image: Union[str, Image.Image],
        seed: Optional[int] = None,
        save_path: str = "./output"
    ) -> str:
        """
        Generate video from text and image (simplified interface)

        Args:
            prompt: Text description for the video
            image: Input image (PIL Image or path to image file)
            seed: Random seed (None for random)
            save_path: Directory to save output video

        Returns:
            str: Path to generated video file
        """
        # Handle image input
        if isinstance(image, Image.Image):
            # Save PIL image to temporary file
            temp_dir = tempfile.mkdtemp()
            image_path = os.path.join(temp_dir, "input_image.png")
            image.save(image_path)
        elif isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            image_path = image
        else:
            raise ValueError("Image must be a PIL Image or path to image file")

        # Create generation arguments
        args = self._create_generation_args(prompt, image_path, seed)
        args.save_path = save_path

        # Create shared models dict to reuse loaded models
        shared_models = {
            "vae": self.vae,
            "model": self.model
        }

        # Generate video using pre-loaded models
        logger.info(f"Generating video with prompt: '{prompt}'")
        gen_settings = get_generation_settings(args)
        vae, latent = generate(args, gen_settings, shared_models)

        # Save and return path
        save_output(args, vae, latent[0], self.device)

        # Find the generated video file
        video_files = glob.glob(os.path.join(save_path, "*.mp4"))
        if video_files:
            latest_video = max(video_files, key=os.path.getctime)
            logger.info(f"Video generated successfully: {latest_video}")
            return latest_video
        else:
            raise RuntimeError("Video generation completed but no video file found")


# Convenience functions
def create_simple_generator(
    dit_path: str,
    vae_path: str,
    text_encoder1_path: str,
    text_encoder2_path: str,
    image_encoder_path: str,
    **kwargs
) -> SimpleFramePackGenerator:
    """
    Convenience function to create a SimpleFramePackGenerator instance

    Args:
        dit_path: Path to DiT model
        vae_path: Path to VAE model
        text_encoder1_path: Path to text encoder 1 (LLaMA)
        text_encoder2_path: Path to text encoder 2 (CLIP)
        image_encoder_path: Path to image encoder
        **kwargs: Additional arguments for SimpleFramePackGenerator

    Returns:
        SimpleFramePackGenerator: Configured generator instance
    """
    return SimpleFramePackGenerator(
        dit_path=dit_path,
        vae_path=vae_path,
        text_encoder1_path=text_encoder1_path,
        text_encoder2_path=text_encoder2_path,
        image_encoder_path=image_encoder_path,
        **kwargs
    )


def generate_video_simple(
    prompt: str,
    image: Union[str, Image.Image],
    dit_path: str,
    vae_path: str,
    text_encoder1_path: str,
    text_encoder2_path: str,
    image_encoder_path: str,
    **kwargs
) -> str:
    """
    Simple one-shot function to generate video

    Args:
        prompt: Text prompt for video generation
        image: Input image (PIL Image or path to image file)
        dit_path: Path to DiT model
        vae_path: Path to VAE model
        text_encoder1_path: Path to text encoder 1 (LLaMA)
        text_encoder2_path: Path to text encoder 2 (CLIP)
        image_encoder_path: Path to image encoder
        **kwargs: Additional generation parameters (video_size, video_frames, etc.)

    Returns:
        str: Path to generated video file
    """
    generator = create_simple_generator(
        dit_path=dit_path,
        vae_path=vae_path,
        text_encoder1_path=text_encoder1_path,
        text_encoder2_path=text_encoder2_path,
        image_encoder_path=image_encoder_path,
        **kwargs
    )

    return generator.generate(prompt=prompt, image=image)


# Example usage
if __name__ == "__main__":
    # Example of how to use the Simplified FramePack API

    # Method 1: Using the class directly (recommended for multiple generations)
    generator = SimpleFramePackGenerator(
        dit_path="/path/to/dit/model",
        vae_path="/path/to/vae/model",
        text_encoder1_path="/path/to/text_encoder1",
        text_encoder2_path="/path/to/text_encoder2",
        image_encoder_path="/path/to/image_encoder",
        device="cuda",  # or "cpu"
        video_size=(512, 512),  # Fixed video size
        video_frames=33,  # Fixed frame count (about 1 second)
        fps=30,
        infer_steps=25,
        fp8=False,
        attn_mode="torch"
    )

    # Generate multiple videos with the same generator (models loaded once)
    video_path1 = generator.generate(
        prompt="A beautiful sunset over the ocean with gentle waves",
        image="/path/to/input/image.jpg",  # or PIL Image object
        seed=42
    )
    print(f"Generated video 1: {video_path1}")

    video_path2 = generator.generate(
        prompt="The same scene but with birds flying",
        image="/path/to/input/image.jpg",
        seed=123
    )
    print(f"Generated video 2: {video_path2}")

    # Method 2: Using the convenience function (for single generation)
    video_path3 = generate_video_simple(
        prompt="A cat playing in the garden",
        image="/path/to/cat_image.jpg",
        dit_path="/path/to/dit/model",
        vae_path="/path/to/vae/model",
        text_encoder1_path="/path/to/text_encoder1",
        text_encoder2_path="/path/to/text_encoder2",
        image_encoder_path="/path/to/image_encoder",
        video_size=(256, 256),  # Can override default parameters
        video_frames=17  # About 0.5 seconds at 30fps
    )
    print(f"Generated video 3: {video_path3}")
