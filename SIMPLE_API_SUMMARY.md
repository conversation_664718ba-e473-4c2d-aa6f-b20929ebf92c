# 简化的 FramePack API 总结

## 核心改进

我已经为你创建了一个极简的FramePack API包装器，主要改进如下：

### 🚀 主要特点

1. **模型只初始化一次**：所有模型（DiT、VAE、文本编码器、图像编码器）在创建生成器时加载一次
2. **极简生成接口**：每次生成只需要传入 `prompt` 和 `image` 两个参数
3. **固定帧数输出**：在初始化时设定固定的视频参数，避免重复配置
4. **高效复用**：多次生成时重用已加载的模型，大幅提升效率

### 📁 文件结构

```
framepack_wrapper.py          # 主要API文件
README_framepack_api.md        # 详细使用说明
test_framepack_api.py          # 测试脚本
SIMPLE_API_SUMMARY.md          # 本总结文档
```

## 🔧 使用方法

### 基本用法（推荐）

```python
from framepack_wrapper import SimpleFramePackGenerator

# 1. 初始化生成器（模型只加载一次）
generator = SimpleFramePackGenerator(
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    video_size=(512, 512),      # 固定视频尺寸
    video_frames=33,            # 固定帧数（约1秒@30fps）
    fps=30,
    infer_steps=25
)

# 2. 生成视频（只需要text和image）
video_path1 = generator.generate(
    prompt="A beautiful sunset over the ocean",
    image="/path/to/image.jpg"
)

video_path2 = generator.generate(
    prompt="The same scene with birds flying",
    image="/path/to/image.jpg"
)
```

### 一次性生成

```python
from framepack_wrapper import generate_video_simple

video_path = generate_video_simple(
    prompt="A cat playing in the garden",
    image="/path/to/image.jpg",
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder"
)
```

## ⚙️ 配置参数

### 初始化时设定（固定参数）
- `video_size`: 视频尺寸，默认 (512, 512)
- `video_frames`: 帧数，默认 33（约1秒@30fps）
- `fps`: 帧率，默认 30
- `infer_steps`: 推理步数，默认 25
- `guidance_scale`: 引导比例，默认 1.0
- `embedded_cfg_scale`: CFG比例，默认 10.0

### 生成时传入（每次可变）
- `prompt`: 文本描述（必需）
- `image`: 输入图像（必需）
- `seed`: 随机种子（可选）
- `save_path`: 保存路径（可选）

## 🎯 优势对比

### 原始API vs 简化API

| 特性 | 原始API | 简化API |
|------|---------|---------|
| 模型加载 | 每次生成都加载 | 只加载一次 |
| 参数配置 | 每次都需要配置所有参数 | 初始化时配置一次 |
| 生成接口 | 需要传入10+个参数 | 只需要传入text和image |
| 内存效率 | 较低（重复加载） | 高（模型复用） |
| 使用复杂度 | 复杂 | 极简 |

## 🚀 性能优化

1. **模型复用**：避免重复加载模型，节省时间和内存
2. **固定参数**：减少参数验证和配置开销
3. **预加载优化**：在初始化时完成所有模型优化

## 📝 快速开始

1. **修改模型路径**：编辑 `test_framepack_api.py` 中的模型路径
2. **运行测试**：`python test_framepack_api.py`
3. **集成到项目**：导入 `SimpleFramePackGenerator` 并使用

## 🔍 示例场景

### 批量生成（高效）
```python
# 初始化一次
generator = SimpleFramePackGenerator(...)

# 批量生成多个视频
prompts = ["scene 1", "scene 2", "scene 3"]
for i, prompt in enumerate(prompts):
    video_path = generator.generate(
        prompt=prompt,
        image=f"input_{i}.jpg"
    )
    print(f"Generated: {video_path}")
```

### 交互式生成
```python
generator = SimpleFramePackGenerator(...)

while True:
    prompt = input("输入描述: ")
    image_path = input("输入图像路径: ")
    
    video_path = generator.generate(prompt=prompt, image=image_path)
    print(f"生成完成: {video_path}")
```

## 🎉 总结

这个简化的API让FramePack的使用变得极其简单：
- **初始化一次，使用多次**
- **只需要传入text和image**
- **自动处理所有复杂配置**
- **高效的模型复用**

现在你可以专注于创意内容，而不用担心复杂的技术配置！
