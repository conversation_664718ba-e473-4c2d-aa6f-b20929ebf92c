#!/usr/bin/env python3
"""
Test script for FramePack API
"""

import os
import sys
from PIL import Image
import numpy as np

# Add current directory to path to import our API
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from framepack_wrapper import FramePackGenerator, generate_video_simple


def create_test_image(size=(512, 512), save_path="test_image.jpg"):
    """Create a simple test image"""
    # Create a simple gradient image
    width, height = size
    image_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Create a gradient from blue to red
    for y in range(height):
        for x in range(width):
            image_array[y, x] = [
                int(255 * x / width),  # Red channel
                int(255 * y / height),  # Green channel
                128  # Blue channel
            ]
    
    image = Image.fromarray(image_array)
    image.save(save_path)
    print(f"Test image created: {save_path}")
    return save_path


def test_framepack_api():
    """Test the FramePack API"""
    
    # 这些路径需要根据你的实际模型路径进行修改
    model_paths = {
        "dit_path": "/path/to/your/dit/model",
        "vae_path": "/path/to/your/vae/model",
        "text_encoder1_path": "/path/to/your/text_encoder1",
        "text_encoder2_path": "/path/to/your/text_encoder2",
        "image_encoder_path": "/path/to/your/image_encoder"
    }
    
    # 检查模型路径是否存在
    missing_paths = []
    for name, path in model_paths.items():
        if not os.path.exists(path):
            missing_paths.append(f"{name}: {path}")
    
    if missing_paths:
        print("错误：以下模型路径不存在，请修改 test_framepack_api.py 中的路径：")
        for path in missing_paths:
            print(f"  - {path}")
        print("\n请在 test_framepack_api.py 中修改 model_paths 字典中的路径。")
        return False
    
    # 创建测试图像
    test_image_path = create_test_image()
    
    try:
        print("测试方法1：使用类接口...")
        
        # 初始化生成器
        generator = FramePackGenerator(
            **model_paths,
            device="cuda" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu",
            fp8=False,
            attn_mode="torch"
        )
        
        # 生成视频
        video_path = generator.generate_video(
            prompt="A beautiful landscape with mountains and trees",
            image=test_image_path,
            video_size=(256, 256),  # 使用较小的尺寸进行测试
            video_seconds=2.0,  # 较短的视频
            fps=15,  # 较低的帧率
            infer_steps=10,  # 较少的推理步数
            seed=42,
            save_path="./test_output"
        )
        
        print(f"✓ 方法1成功！视频保存到: {video_path}")
        
    except Exception as e:
        print(f"✗ 方法1失败: {e}")
        return False
    
    try:
        print("\n测试方法2：使用便利函数...")
        
        # 使用便利函数
        video_path2 = generate_video_simple(
            prompt="A peaceful garden scene",
            image=test_image_path,
            **model_paths,
            video_size=(256, 256),
            video_seconds=2.0,
            fps=15,
            infer_steps=10,
            save_path="./test_output"
        )
        
        print(f"✓ 方法2成功！视频保存到: {video_path2}")
        
    except Exception as e:
        print(f"✗ 方法2失败: {e}")
        return False
    
    try:
        print("\n测试方法3：使用PIL图像...")
        
        # 加载PIL图像
        pil_image = Image.open(test_image_path)
        
        generator = FramePackGenerator(**model_paths)
        
        video_path3 = generator.generate_video(
            prompt="A dynamic scene with movement",
            image=pil_image,  # 直接使用PIL图像
            video_size=(256, 256),
            video_seconds=2.0,
            fps=15,
            infer_steps=10,
            save_path="./test_output"
        )
        
        print(f"✓ 方法3成功！视频保存到: {video_path3}")
        
    except Exception as e:
        print(f"✗ 方法3失败: {e}")
        return False
    
    # 清理测试文件
    if os.path.exists(test_image_path):
        os.remove(test_image_path)
        print(f"\n清理测试图像: {test_image_path}")
    
    print("\n🎉 所有测试通过！FramePack API 工作正常。")
    return True


if __name__ == "__main__":
    print("FramePack API 测试脚本")
    print("=" * 50)
    
    # 提示用户修改模型路径
    print("注意：在运行此测试之前，请确保：")
    print("1. 修改 test_framepack_api.py 中的模型路径")
    print("2. 确保所有必要的依赖项已安装")
    print("3. 确保有足够的GPU内存（如果使用GPU）")
    print()
    
    success = test_framepack_api()
    
    if not success:
        print("\n测试失败。请检查错误信息并修复问题。")
        sys.exit(1)
    else:
        print("\n测试完成！")
