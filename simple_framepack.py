"""
Ultra-Simple FramePack API - Direct latent generation
Only calls sample_hunyuan once to generate a single latent section
"""

import os
import tempfile
import torch
from typing import Union, Optional
from PIL import Image
import logging
import random

# Import necessary modules
from fpack_generate_video import (
    load_dit_model,
    optimize_model,
    prepare_i2v_inputs
)
from frame_pack.framepack_utils import load_vae
from frame_pack.k_diffusion_hunyuan import sample_hunyuan
from types import SimpleNamespace

logger = logging.getLogger(__name__)


class UltraSimpleFramePack:
    """
    Ultra-simplified FramePack API
    - Models loaded once during initialization
    - Single latent section generation per call
    - Only requires text and image input
    """
    
    def __init__(
        self,
        dit_path: str = "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7",
        vae_path: str = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors",
        text_encoder1_path: str = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773",
        text_encoder2_path: str = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773",
        image_encoder_path: str = "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--lllyasviel--flux_redux_bfl/snapshots/45b801affc54ff2af4e5daf1b282e0921901db87/image_encoder/model.safetensors",
        device: Optional[str] = None,
        video_size: tuple = (512, 768),  # From your config: 512x768
        frames: int = 33,  # Fixed frame count for single section
        infer_steps: int = 25,  # From your config
        guidance_scale: float = 1.0,
        embedded_cfg_scale: float = 10.0,
        fp8: bool = False,
        fp8_scaled: bool = True,  # From your config: --fp8_scaled
        attn_mode: str = "sdpa",  # From your config: --attn_mode sdpa
        vae_chunk_size: Optional[int] = 32,  # From your config
        vae_spatial_tile_sample_min_size: Optional[int] = 128,  # From your config
        f1: bool = True,  # From your config: --f1
        lora_weight: Optional[str] = "/media/jiayueru/Codes/musubi-tuner/ckpt/output/test_lora-000005.safetensors",  # From your config
        lora_multiplier: float = 1.0,  # From your config
        seed: Optional[int] = 1234  # From your config
    ):
        """
        Initialize with all models loaded once
        
        Args:
            dit_path: Path to DiT model
            vae_path: Path to VAE model
            text_encoder1_path: Path to text encoder 1 (LLaMA)
            text_encoder2_path: Path to text encoder 2 (CLIP)
            image_encoder_path: Path to image encoder
            device: Device to use
            video_size: Fixed video dimensions (height, width)
            frames: Fixed number of frames for single section
            infer_steps: Fixed inference steps
            guidance_scale: Fixed guidance scale
            embedded_cfg_scale: Fixed embedded CFG scale
            fp8: Use FP8 precision
            fp8_scaled: Use scaled FP8 precision
            attn_mode: Attention mode
        """
        # Set device
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        # Store fixed parameters
        self.video_size = video_size
        self.frames = frames
        self.infer_steps = infer_steps
        self.guidance_scale = guidance_scale
        self.embedded_cfg_scale = embedded_cfg_scale
        self.vae_chunk_size = vae_chunk_size
        self.vae_spatial_tile_sample_min_size = vae_spatial_tile_sample_min_size
        self.f1 = f1
        self.lora_weight = lora_weight
        self.lora_multiplier = lora_multiplier
        self.default_seed = seed

        logger.info(f"Initializing UltraSimpleFramePack on device: {self.device}")
        logger.info(f"Fixed parameters: {video_size}@{frames}frames, {infer_steps}steps")
        logger.info(f"VAE settings: chunk_size={vae_chunk_size}, tile_min_size={vae_spatial_tile_sample_min_size}")
        logger.info(f"LoRA: {lora_weight}, multiplier={lora_multiplier}")

        # Create base args for model loading
        self.base_args = self._create_base_args(
            dit_path, vae_path, text_encoder1_path, text_encoder2_path,
            image_encoder_path, fp8, fp8_scaled, attn_mode
        )
        
        # Load all models once
        logger.info("Loading models...")
        self._load_models()
        logger.info("All models loaded successfully!")
    
    def _create_base_args(
        self, dit_path, vae_path, text_encoder1_path, text_encoder2_path, 
        image_encoder_path, fp8, fp8_scaled, attn_mode
    ) -> SimpleNamespace:
        """Create base arguments for model loading"""
        args = SimpleNamespace()
        
        # Model paths
        args.dit = dit_path
        args.vae = vae_path
        args.text_encoder1 = text_encoder1_path
        args.text_encoder2 = text_encoder2_path
        args.image_encoder = image_encoder_path
        
        # Device and precision settings
        args.device = self.device
        args.fp8 = fp8
        args.fp8_scaled = fp8_scaled
        args.fp8_llm = False
        args.attn_mode = attn_mode
        
        # Default settings for model loading
        args.blocks_to_swap = 0
        args.vae_chunk_size = self.vae_chunk_size
        args.vae_spatial_tile_sample_min_size = self.vae_spatial_tile_sample_min_size
        args.lora_weight = self.lora_weight
        args.lora_multiplier = self.lora_multiplier
        args.save_merged_model = None
        
        return args
    
    def _load_models(self):
        """Load all models once during initialization"""
        # Load VAE
        logger.info("Loading VAE...")
        self.vae = load_vae(
            self.base_args.vae,
            self.vae_chunk_size,
            self.vae_spatial_tile_sample_min_size,
            self.device
        )
        
        # Load DiT model
        logger.info("Loading DiT model...")
        self.model = load_dit_model(self.base_args, self.device)
        optimize_model(self.model, self.base_args, self.device)
        
        logger.info("Models loaded and ready for generation!")
    
    def _create_generation_args(
        self, prompt: str, image_path: str, seed: Optional[int] = None
    ) -> SimpleNamespace:
        """Create arguments for single section generation"""
        args = SimpleNamespace()
        
        # Copy base args
        for attr in vars(self.base_args):
            setattr(args, attr, getattr(self.base_args, attr))
        
        # Generation parameters
        args.prompt = prompt
        args.image_path = image_path
        args.video_size = list(self.video_size)
        args.video_seconds = self.frames / 30.0  # Convert frames to seconds
        args.fps = 30
        args.infer_steps = self.infer_steps
        args.guidance_scale = self.guidance_scale
        args.embedded_cfg_scale = self.embedded_cfg_scale
        args.negative_prompt = ""
        args.seed = seed
        args.sample_solver = "unipc"
        
        # Single section settings
        args.latent_window_size = 9
        args.guidance_rescale = 0.0
        args.end_image_path = None
        args.latent_paddings = None
        args.one_frame_inference = None
        args.f1 = self.f1
        args.bulk_decode = False
        args.output_type = "latent"
        args.no_metadata = False
        args.video_sections = None
        args.custom_system_prompt = None
        
        return args
    
    def generate_latent(
        self, 
        prompt: str, 
        image: Union[str, Image.Image], 
        seed: Optional[int] = None
    ) -> torch.Tensor:
        """
        Generate a single latent section directly using sample_hunyuan
        
        Args:
            prompt: Text description for the video
            image: Input image (PIL Image or path to image file)
            seed: Random seed (None for random)
            
        Returns:
            torch.Tensor: Generated latent tensor
        """
        # Handle image input
        if isinstance(image, Image.Image):
            temp_dir = tempfile.mkdtemp()
            image_path = os.path.join(temp_dir, "input_image.png")
            image.save(image_path)
        elif isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            image_path = image
        else:
            raise ValueError("Image must be a PIL Image or path to image file")
        
        # Create generation arguments
        args = self._create_generation_args(prompt, image_path, seed)
        
        # Prepare inputs (text encoding, image encoding, etc.)
        logger.info(f"Preparing inputs for prompt: '{prompt}'")
        height, width, _, context, context_null, context_img, _ = prepare_i2v_inputs(
            args, self.device, self.vae
        )
        
        # Prepare seed
        if seed is None:
            seed = self.default_seed if self.default_seed is not None else random.randint(0, 2**32 - 1)
        seed_g = torch.Generator(device="cpu")
        seed_g.manual_seed(seed)
        
        # Get conditioning data
        context_for_index = context[0]  # Use first (and only) context
        llama_vec = context_for_index["llama_vec"].to(self.device, dtype=torch.bfloat16)
        llama_attention_mask = context_for_index["llama_attention_mask"].to(self.device)
        clip_l_pooler = context_for_index["clip_l_pooler"].to(self.device, dtype=torch.bfloat16)
        
        image_encoder_last_hidden_state = context_img[0]["image_encoder_last_hidden_state"].to(
            self.device, dtype=torch.bfloat16
        )
        
        llama_vec_n = context_null["llama_vec"].to(self.device, dtype=torch.bfloat16)
        llama_attention_mask_n = context_null["llama_attention_mask"].to(self.device)
        clip_l_pooler_n = context_null["clip_l_pooler"].to(self.device, dtype=torch.bfloat16)
        
        # Prepare latent indices (simplified for single section)
        latent_indices = torch.arange(0, args.latent_window_size).unsqueeze(0)
        clean_latent_indices = torch.arange(0, 1).unsqueeze(0)  # Just start frame
        
        # Get start latent
        start_latent = context_img[0]["start_latent"].to(self.device)
        clean_latents = start_latent
        
        # No 2x/4x clean latents for single section
        clean_latents_2x = None
        clean_latent_2x_indices = None
        clean_latents_4x = None
        clean_latent_4x_indices = None
        
        # Direct call to sample_hunyuan (the core function you want to use)
        logger.info("Calling sample_hunyuan directly...")
        generated_latents = sample_hunyuan(
            transformer=self.model,
            sampler=args.sample_solver,
            width=width,
            height=height,
            frames=self.frames,
            real_guidance_scale=args.guidance_scale,
            distilled_guidance_scale=args.embedded_cfg_scale,
            guidance_rescale=args.guidance_rescale,
            num_inference_steps=args.infer_steps,
            generator=seed_g,
            prompt_embeds=llama_vec,
            prompt_embeds_mask=llama_attention_mask,
            prompt_poolers=clip_l_pooler,
            negative_prompt_embeds=llama_vec_n,
            negative_prompt_embeds_mask=llama_attention_mask_n,
            negative_prompt_poolers=clip_l_pooler_n,
            device=self.device,
            dtype=torch.bfloat16,
            image_embeddings=image_encoder_last_hidden_state,
            latent_indices=latent_indices,
            clean_latents=clean_latents,
            clean_latent_indices=clean_latent_indices,
            clean_latents_2x=clean_latents_2x,
            clean_latent_2x_indices=clean_latent_2x_indices,
            clean_latents_4x=clean_latents_4x,
            clean_latent_4x_indices=clean_latent_4x_indices,
        )
        
        logger.info(f"Generated latent shape: {generated_latents.shape}")
        return generated_latents


# Convenience function
def create_ultra_simple_generator(
    dit_path: str,
    vae_path: str,
    text_encoder1_path: str,
    text_encoder2_path: str,
    image_encoder_path: str,
    **kwargs
) -> UltraSimpleFramePack:
    """Create an UltraSimpleFramePack instance"""
    return UltraSimpleFramePack(
        dit_path=dit_path,
        vae_path=vae_path,
        text_encoder1_path=text_encoder1_path,
        text_encoder2_path=text_encoder2_path,
        image_encoder_path=image_encoder_path,
        **kwargs
    )


# Example usage
if __name__ == "__main__":
    # Initialize once
    generator = UltraSimpleFramePack(
        dit_path="/path/to/dit/model",
        vae_path="/path/to/vae/model",
        text_encoder1_path="/path/to/text_encoder1",
        text_encoder2_path="/path/to/text_encoder2",
        image_encoder_path="/path/to/image_encoder",
        video_size=(512, 512),
        frames=33  # Single section with 33 frames
    )
    
    # Generate latent directly
    latent = generator.generate_latent(
        prompt="A beautiful sunset over the ocean",
        image="/path/to/image.jpg",
        seed=42
    )
    
    print(f"Generated latent shape: {latent.shape}")
