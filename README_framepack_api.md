# 简化的 FramePack API

这是一个极简的API包装器，用于调用FramePack视频生成功能。**模型只需要初始化一次**，然后每次生成只需要传入**文本和图像**即可，生成**固定帧数**的视频。

## 功能特点

- **极简接口**：模型初始化一次，生成时只需传入text和image
- **固定参数**：视频尺寸、帧数、FPS等在初始化时设定，避免重复配置
- **高效复用**：多次生成视频时重用已加载的模型，大幅提升效率
- **支持PIL图像**：可直接使用PIL图像对象或图像文件路径
- **自动管理**：自动处理临时文件和输出路径

## 安装要求

确保你已经安装了所有必要的依赖项，包括：
- torch
- PIL (Pillow)
- 所有FramePack相关的模块

## 使用方法

### 方法1：使用类接口（推荐，适合多次生成）

```python
from framepack_wrapper import SimpleFramePackGenerator
from PIL import Image

# 初始化生成器（模型只加载一次）
generator = SimpleFramePackGenerator(
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    device="cuda",  # 或 "cpu"
    video_size=(512, 512),  # 固定视频尺寸
    video_frames=33,  # 固定帧数（约1秒@30fps）
    fps=30,
    infer_steps=25,
    fp8=False,
    attn_mode="torch"
)

# 生成多个视频（只需要传入text和image）
video_path1 = generator.generate(
    prompt="A beautiful sunset over the ocean with gentle waves",
    image="/path/to/input/image.jpg",  # 或 PIL Image 对象
    seed=42
)

video_path2 = generator.generate(
    prompt="The same scene but with birds flying",
    image="/path/to/input/image.jpg",
    seed=123
)

print(f"生成的视频1: {video_path1}")
print(f"生成的视频2: {video_path2}")
```

### 方法2：使用便利函数（适合单次生成）

```python
from framepack_wrapper import generate_video_simple

# 一次性生成视频
video_path = generate_video_simple(
    prompt="A cat playing in the garden",
    image="/path/to/cat_image.jpg",
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    video_size=(256, 256),  # 可以覆盖默认参数
    video_frames=17  # 约0.5秒@30fps
)

print(f"生成的视频保存到: {video_path}")
```

### 方法3：使用PIL图像

```python
from PIL import Image
from framepack_wrapper import SimpleFramePackGenerator

# 加载图像
image = Image.open("/path/to/image.jpg")

# 初始化生成器
generator = SimpleFramePackGenerator(
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    video_size=(512, 512),
    video_frames=33
)

# 直接使用PIL图像
video_path = generator.generate(
    prompt="描述视频内容的文本",
    image=image  # 直接传入PIL图像
)
```

## 参数说明

### SimpleFramePackGenerator 初始化参数

**必需参数（模型路径）：**
- `dit_path`: DiT模型路径
- `vae_path`: VAE模型路径
- `text_encoder1_path`: 文本编码器1路径 (LLaMA)
- `text_encoder2_path`: 文本编码器2路径 (CLIP)
- `image_encoder_path`: 图像编码器路径

**可选参数（固定配置）：**
- `device`: 设备 ('cuda', 'cpu', 或 None 自动选择)
- `video_size`: 固定视频尺寸 (height, width)，默认 (512, 512)
- `video_frames`: 固定帧数，默认 33（约1秒@30fps）
- `fps`: 固定帧率，默认 30
- `infer_steps`: 固定推理步数，默认 25
- `guidance_scale`: 固定引导比例，默认 1.0
- `embedded_cfg_scale`: 固定嵌入式CFG比例，默认 10.0
- `fp8`: 是否使用FP8精度，默认 False
- `fp8_scaled`: 是否使用缩放FP8精度，默认 False
- `attn_mode`: 注意力模式，默认 'torch'

### generate 方法参数

**必需参数：**
- `prompt`: 视频生成的文本提示
- `image`: 输入图像 (PIL Image 或图像文件路径)

**可选参数：**
- `seed`: 随机种子（None为随机）
- `save_path`: 输出保存目录，默认 "./output"

## 注意事项

1. 确保所有模型路径都是正确的
2. 确保有足够的GPU内存来运行模型
3. 输入图像会被自动调整到指定的视频尺寸
4. 生成的视频将保存为MP4格式

## 错误处理

API会处理常见的错误情况：
- 图像文件不存在
- 无效的图像格式
- 模型加载失败
- 生成过程中的错误

如果遇到问题，请检查：
1. 模型路径是否正确
2. 输入图像是否有效
3. 设备内存是否充足
4. 依赖项是否正确安装

## 快速开始

1. **准备模型文件**

   确保你有以下模型文件：
   - DiT模型 (Diffusion Transformer)
   - VAE模型 (Variational Autoencoder)
   - 文本编码器1 (LLaMA)
   - 文本编码器2 (CLIP)
   - 图像编码器

2. **修改测试脚本**

   编辑 `test_framepack_api.py` 文件，将模型路径修改为你的实际路径：

   ```python
   model_paths = {
       "dit_path": "/your/actual/path/to/dit/model",
       "vae_path": "/your/actual/path/to/vae/model",
       "text_encoder1_path": "/your/actual/path/to/text_encoder1",
       "text_encoder2_path": "/your/actual/path/to/text_encoder2",
       "image_encoder_path": "/your/actual/path/to/image_encoder"
   }
   ```

3. **运行测试**

   ```bash
   python test_framepack_api.py
   ```

## 文件说明

- `framepack_wrapper.py`: 主要的API包装器
- `README_framepack_api.md`: 使用说明文档
- `test_framepack_api.py`: 测试脚本

## 性能优化建议

1. **使用FP8精度**：如果你的GPU支持，可以启用FP8来节省内存
2. **调整视频参数**：较小的视频尺寸和较短的时长可以减少内存使用
3. **批量处理**：如果需要生成多个视频，可以重用同一个生成器实例

## 常见问题

**Q: 内存不足怎么办？**
A: 尝试减小视频尺寸、缩短视频时长，或启用FP8精度模式。

**Q: 生成速度很慢？**
A: 确保使用GPU，并考虑减少推理步数（infer_steps）。

**Q: 生成的视频质量不好？**
A: 尝试增加推理步数，使用更高的分辨率，或调整guidance_scale参数。
