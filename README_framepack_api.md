# FramePack API

这是一个简单的API包装器，用于调用FramePack视频生成功能。它提供了一个简洁的接口，让你可以通过传入图像和文本提示来生成视频。

## 功能特点

- 简单易用的API接口
- 支持PIL图像和图像文件路径作为输入
- 可配置的视频参数（尺寸、时长、FPS等）
- 支持多种精度模式（FP8、FP8 scaled等）
- 可选择返回视频文件路径或视频张量

## 安装要求

确保你已经安装了所有必要的依赖项，包括：
- torch
- PIL (Pillow)
- 所有FramePack相关的模块

## 使用方法

### 方法1：使用类接口

```python
from framepack_wrapper import FramePackGenerator
from PIL import Image

# 初始化生成器
generator = FramePackGenerator(
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model", 
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    device="cuda",  # 或 "cpu"
    fp8=False,
    attn_mode="torch"
)

# 生成视频
video_path = generator.generate_video(
    prompt="A beautiful sunset over the ocean with gentle waves",
    image="/path/to/input/image.jpg",  # 或 PIL Image 对象
    video_size=(512, 512),
    video_seconds=5.0,
    fps=30,
    infer_steps=25,
    seed=42,
    save_path="./output"
)

print(f"生成的视频保存到: {video_path}")
```

### 方法2：使用便利函数

```python
from framepack_wrapper import generate_video_simple

# 一次性生成视频
video_path = generate_video_simple(
    prompt="A cat playing in the garden",
    image="/path/to/cat_image.jpg",
    dit_path="/path/to/dit/model",
    vae_path="/path/to/vae/model",
    text_encoder1_path="/path/to/text_encoder1",
    text_encoder2_path="/path/to/text_encoder2",
    image_encoder_path="/path/to/image_encoder",
    video_size=(256, 256),
    video_seconds=3.0,
    save_path="./output"
)

print(f"生成的视频保存到: {video_path}")
```

### 方法3：使用PIL图像

```python
from PIL import Image
from framepack_wrapper import FramePackGenerator

# 加载图像
image = Image.open("/path/to/image.jpg")

# 初始化生成器
generator = FramePackGenerator(...)

# 直接使用PIL图像
video_path = generator.generate_video(
    prompt="描述视频内容的文本",
    image=image,  # 直接传入PIL图像
    video_size=(512, 512),
    video_seconds=5.0
)
```

## 参数说明

### FramePackGenerator 初始化参数

- `dit_path`: DiT模型路径
- `vae_path`: VAE模型路径
- `text_encoder1_path`: 文本编码器1路径 (LLaMA)
- `text_encoder2_path`: 文本编码器2路径 (CLIP)
- `image_encoder_path`: 图像编码器路径
- `device`: 设备 ('cuda', 'cpu', 或 None 自动选择)
- `fp8`: 是否使用FP8精度
- `fp8_scaled`: 是否使用缩放FP8精度
- `attn_mode`: 注意力模式 ('torch', 'flash', 'sageattn', 'xformers', 'sdpa')

### generate_video 参数

- `prompt`: 视频生成的文本提示
- `image`: 输入图像 (PIL Image 或图像文件路径)
- `video_size`: 视频尺寸 (height, width)
- `video_seconds`: 视频时长（秒）
- `fps`: 帧率
- `infer_steps`: 推理步数
- `guidance_scale`: 引导比例
- `embedded_cfg_scale`: 嵌入式CFG比例
- `negative_prompt`: 负面提示
- `seed`: 随机种子
- `sample_solver`: 采样求解器 ('unipc', 'dpm++', 'vanilla')
- `save_path`: 输出保存目录
- `return_video_path`: 是否返回视频路径（否则返回视频张量）

## 注意事项

1. 确保所有模型路径都是正确的
2. 确保有足够的GPU内存来运行模型
3. 输入图像会被自动调整到指定的视频尺寸
4. 生成的视频将保存为MP4格式

## 错误处理

API会处理常见的错误情况：
- 图像文件不存在
- 无效的图像格式
- 模型加载失败
- 生成过程中的错误

如果遇到问题，请检查：
1. 模型路径是否正确
2. 输入图像是否有效
3. 设备内存是否充足
4. 依赖项是否正确安装

## 快速开始

1. **准备模型文件**

   确保你有以下模型文件：
   - DiT模型 (Diffusion Transformer)
   - VAE模型 (Variational Autoencoder)
   - 文本编码器1 (LLaMA)
   - 文本编码器2 (CLIP)
   - 图像编码器

2. **修改测试脚本**

   编辑 `test_framepack_api.py` 文件，将模型路径修改为你的实际路径：

   ```python
   model_paths = {
       "dit_path": "/your/actual/path/to/dit/model",
       "vae_path": "/your/actual/path/to/vae/model",
       "text_encoder1_path": "/your/actual/path/to/text_encoder1",
       "text_encoder2_path": "/your/actual/path/to/text_encoder2",
       "image_encoder_path": "/your/actual/path/to/image_encoder"
   }
   ```

3. **运行测试**

   ```bash
   python test_framepack_api.py
   ```

## 文件说明

- `framepack_wrapper.py`: 主要的API包装器
- `README_framepack_api.md`: 使用说明文档
- `test_framepack_api.py`: 测试脚本

## 性能优化建议

1. **使用FP8精度**：如果你的GPU支持，可以启用FP8来节省内存
2. **调整视频参数**：较小的视频尺寸和较短的时长可以减少内存使用
3. **批量处理**：如果需要生成多个视频，可以重用同一个生成器实例

## 常见问题

**Q: 内存不足怎么办？**
A: 尝试减小视频尺寸、缩短视频时长，或启用FP8精度模式。

**Q: 生成速度很慢？**
A: 确保使用GPU，并考虑减少推理步数（infer_steps）。

**Q: 生成的视频质量不好？**
A: 尝试增加推理步数，使用更高的分辨率，或调整guidance_scale参数。
