#!/usr/bin/env python3
"""
Test script for Ultra-Simple FramePack API
"""

import os
import sys
from PIL import Image
import numpy as np
import torch

# Add current directory to path to import our API
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from simple_framepack import UltraSimpleFramePack


def create_test_image(size=(512, 512), save_path="test_image.jpg"):
    """Create a simple test image"""
    width, height = size
    image_array = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Create a gradient from blue to red
    for y in range(height):
        for x in range(width):
            image_array[y, x] = [
                int(255 * x / width),  # Red channel
                int(255 * y / height),  # Green channel
                128  # Blue channel
            ]
    
    image = Image.fromarray(image_array)
    image.save(save_path)
    print(f"Test image created: {save_path}")
    return save_path


def test_ultra_simple_api():
    """Test the Ultra-Simple FramePack API"""

    # 现在使用默认路径，如果需要可以覆盖
    print("使用默认模型路径（来自你的配置）...")

    # 检查默认路径是否存在
    default_paths = {
        "dit_path": "/media/jiayueru/Codes/FramePack/hf_download/hub/models--lllyasviel--FramePack_F1_I2V_HY_20250503/snapshots/ab239828e0b384fed75580f186f078717d4020f7",
        "vae_path": "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773/vae/diffusion_pytorch_model.safetensors",
        "text_encoder1_path": "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773",
        "text_encoder2_path": "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--hunyuanvideo-community--HunyuanVideo/snapshots/e8c2aaa66fe3742a32c11a6766aecbf07c56e773",
        "image_encoder_path": "/media/jiayueru/Codes/musubi-tuner/ckpt/hf_download/hub/models--lllyasviel--flux_redux_bfl/snapshots/45b801affc54ff2af4e5daf1b282e0921901db87/image_encoder/model.safetensors",
        "lora_weight": "/media/jiayueru/Codes/musubi-tuner/ckpt/output/test_lora-000005.safetensors"
    }

    missing_paths = []
    for name, path in default_paths.items():
        if not os.path.exists(path):
            missing_paths.append(f"{name}: {path}")

    if missing_paths:
        print("警告：以下默认路径不存在，测试可能失败：")
        for path in missing_paths:
            print(f"  - {path}")
        print("\n如果路径不正确，请修改 simple_framepack.py 中的默认路径。")
        # 继续测试，但可能会失败
    
    # 创建测试图像
    test_image_path = create_test_image()
    
    try:
        print("测试Ultra-Simple API...")
        
        # 初始化生成器（使用默认路径，只覆盖测试参数）
        generator = UltraSimpleFramePack(
            device="cuda" if torch.cuda.is_available() else "cpu",
            video_size=(256, 256),  # 较小尺寸用于测试
            frames=17,  # 较少帧数用于测试
            infer_steps=10  # 较少推理步数用于快速测试
        )
        
        print("✓ 模型加载成功！")
        
        # 测试1：生成latent（使用图像路径）
        print("\n测试1：使用图像路径生成latent...")
        latent1 = generator.generate_latent(
            prompt="A beautiful landscape with mountains and trees",
            image=test_image_path,
            seed=42
        )
        print(f"✓ 生成成功！Latent shape: {latent1.shape}")
        
        # 测试2：生成latent（使用PIL图像）
        print("\n测试2：使用PIL图像生成latent...")
        pil_image = Image.open(test_image_path)
        latent2 = generator.generate_latent(
            prompt="A peaceful garden scene",
            image=pil_image,
            seed=123
        )
        print(f"✓ 生成成功！Latent shape: {latent2.shape}")
        
        # 测试3：批量生成多个latent
        print("\n测试3：批量生成多个latent...")
        prompts = [
            "A sunset over the ocean",
            "Birds flying in the sky", 
            "Clouds moving slowly"
        ]
        
        latents = []
        for i, prompt in enumerate(prompts):
            latent = generator.generate_latent(
                prompt=prompt,
                image=test_image_path,
                seed=i * 100
            )
            latents.append(latent)
            print(f"  ✓ Latent {i+1}: {latent.shape}")
        
        print(f"\n✓ 批量生成成功！共生成 {len(latents)} 个latent")
        
        # 验证latent的基本属性
        print("\n验证latent属性...")
        expected_shape = (1, 16, (17 + 3) // 4, 256 // 8, 256 // 8)  # (B, C, T, H, W)
        if latent1.shape == expected_shape:
            print(f"✓ Latent shape正确: {latent1.shape}")
        else:
            print(f"⚠ Latent shape不符合预期: {latent1.shape}, 期望: {expected_shape}")
        
        print(f"✓ Latent数据类型: {latent1.dtype}")
        print(f"✓ Latent设备: {latent1.device}")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            print(f"\n清理测试图像: {test_image_path}")
    
    print("\n🎉 所有测试通过！Ultra-Simple FramePack API 工作正常。")
    print("\n📝 使用说明：")
    print("1. 初始化生成器时，所有模型只加载一次")
    print("2. 调用 generate_latent() 直接生成单个latent section")
    print("3. 每次调用只需要传入 prompt 和 image")
    print("4. 返回的是原始latent tensor，可以进一步处理或解码")
    
    return True


if __name__ == "__main__":
    print("Ultra-Simple FramePack API 测试脚本")
    print("=" * 50)
    
    print("注意：在运行此测试之前，请确保：")
    print("1. 修改 test_ultra_simple.py 中的模型路径")
    print("2. 确保所有必要的依赖项已安装")
    print("3. 确保有足够的GPU内存（如果使用GPU）")
    print()
    
    success = test_ultra_simple_api()
    
    if not success:
        print("\n测试失败。请检查错误信息并修复问题。")
        sys.exit(1)
    else:
        print("\n测试完成！")
